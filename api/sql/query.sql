-- name: GetAllEngagements :many
SELECT *
FROM engagements
WHERE engagements.is_active = true
ORDER BY engagements.created_at DESC;

-- name: GetUserEngagements :many
SELECT *
FROM engagements
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE users.id = $1
  AND engagements.is_active = true;

-- name: GetNodeTypeCloudInstance :one
SELECT provider,
	   region,
	   operating_system_image_id,
	   node_type_cloud_instances.instance_type          as instance_size,
	   node_type_cloud_instances.name                   as instance_name,
	   open_ports,
	   public_ipv4_address,
	   node_type_cloud_instances.node_id,
	   node_type,
	   node_group_id,
	   n.is_deleted                                     as node_is_deleted,
	   ng.name                                          as node_group_name,
	   ng.is_active                                     as node_group_is_active,
	   ng.created_at                                    as node_group_created_at,
	   ng.updated_at                                    as node_group_updated_at,
	   e.id                                             as engagement_id,
	   e.is_active                                      as engagement_is_active,
	   e.status                                         as engagement_status,
	   u.id                                             as user_id,
	   u.username,                                       
     COALESCE(d.status, 'ERROR')                     AS CI_deployment_status,
     cloud_instance_state,
     cloud_instance_id
FROM node_type_cloud_instances
         JOIN nodes n ON n.id = node_type_cloud_instances.node_id
         JOIN node_groups ng ON n.node_group_id = ng.id
         JOIN engagements e ON ng.engagement_id = e.id
         JOIN engagements_users ON e.id = engagements_users.engagement_id
         JOIN users u ON engagements_users.user_id = u.id
         LEFT JOIN (
              SELECT DISTINCT ON (deployments.node_id) 
                      deployments.node_id, 
                      deployments.status
              FROM deployments
              ORDER BY deployments.node_id, deployments.created_at DESC
         ) d ON d.node_id = node_type_cloud_instances.node_id
WHERE node_type_cloud_instances.node_id = $1
  AND u.id = $2
  AND n.is_deleted = false;

-- name: GetNodeTypeEmailAddress :one
SELECT *
FROM node_type_email_addresses
         JOIN nodes n on n.id = node_type_email_addresses.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE node_id = $1
  AND users.id = $2
  AND n.is_deleted = false;

-- name: GetNodeTypeHost :one
SELECT *
FROM node_type_hosts
         JOIN nodes n on n.id = node_type_hosts.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE node_id = $1
  AND users.id = $2
  AND n.is_deleted = false;

-- name: GetNodeTypePerson :one
SELECT *
FROM node_type_persons
         JOIN nodes n on n.id = node_type_persons.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE node_id = $1
  AND users.id = $2
  AND n.is_deleted = false;

-- name: GetNodeTypeUrl :one
SELECT
    node_type_urls.url,
    node_type_urls.node_id,
    n.id,
    n.node_type,
    n.name,
    n.node_group_id,
    n.is_deleted,
    ng.id,
    ng.name,
    ng.is_active,
    ng.created_at,
    ng.updated_at,
    ng.engagement_id,
    engagements.id,
    engagements.title,
    engagements.wbs_code,
    engagements.is_active,
    engagements.client_id,
    engagements.created_at,
    engagements.updated_at,
    engagements.status,
    engagements.error_message,
    engagements_users.engagement_id,
    engagements_users.user_id,
    users.id,
    users.username,
    users.custom_username,
    users.full_name,
    users.app_role,
    users.ssh_key,
    users.ssh_key_label,
    users.ssh_key_creation_date,
    users.is_inactive
FROM node_type_urls
         JOIN nodes n on n.id = node_type_urls.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE node_id = $1
  AND users.id = $2
  AND n.is_deleted = false;

-- name: ListEngagementsWithUsers :many
SELECT engagements.id,
       engagements.status,
       engagements.error_message,
       engagements.title      as engagement_title,
       engagements.wbs_code,
       engagements.is_active,
       engagements.created_at as engagement_created_at,
       engagements.updated_at as engagement_updated_at,
       clients.name           as client_name,
       users.id               as user_id,
       users.full_name,
       users.username,
       users.custom_username,
       users.ssh_key
FROM engagements
         JOIN clients on clients.id = engagements.client_id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
ORDER BY engagement_updated_at DESC;

-- name: ListEngagementsWithStandardUsers :many
SELECT engagements.id,  
       engagements.status,
       engagements.error_message,
       engagements.title      as engagement_title,
       engagements.wbs_code,
       engagements.is_active,
       engagements.created_at as engagement_created_at,
       engagements.updated_at as engagement_updated_at,
       clients.name           as client_name,
       users.id               as user_id,
       users.full_name,
       users.username,
       users.custom_username,
       users.ssh_key
FROM engagements
         JOIN clients on clients.id = engagements.client_id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE users.id = $1
ORDER BY engagement_updated_at DESC;

-- name: GetEngagement :many
SELECT engagements.id,
       engagements.status,
       engagements.error_message,
       engagements.title      as engagement_title,
       engagements.wbs_code,
       engagements.is_active,
       engagements.created_at as engagement_created_at,
       engagements.updated_at as engagement_updated_at,
       clients.name           as client_name,
       users.id               as user_id,
       users.full_name,
       users.username,
       users.custom_username,
       users.ssh_key
FROM engagements
         JOIN clients on clients.id = engagements.client_id
         LEFT JOIN engagements_users on engagements.id = engagements_users.engagement_id
         LEFT JOIN users on engagements_users.user_id = users.id
WHERE engagements.id = $1
  AND engagements.is_active = true;

-- name: GetEngagementByTitle :many
SELECT engagements.id,
       engagements.status,
       engagements.error_message,
       engagements.title      as engagement_title,
       engagements.wbs_code,
       engagements.is_active,
       engagements.created_at as engagement_created_at,
       engagements.updated_at as engagement_updated_at,
       clients.name           as client_name,
       users.id               as user_id,
       users.full_name,
       users.username,
       users.custom_username,
       users.ssh_key
FROM engagements
         JOIN clients on clients.id = engagements.client_id
         LEFT JOIN engagements_users on engagements.id = engagements_users.engagement_id
         LEFT JOIN users on engagements_users.user_id = users.id
WHERE engagements.title = $1
  AND engagements.is_active = true;

-- name: GetEngagementCloudInstancesForUser :many
SELECT n.id                                  as node_id,
       ng.id                                 as node_group_id,
       ng.name                               as node_group_name,
       ng.is_active                          as node_group_is_active,
       ng.created_at                         as node_group_created_at,
       ng.updated_at                         as node_group_updated_at,
       engagements.title,
       node_type_cloud_instances.operating_system_image_id,
       node_type_cloud_instances.provider,
       node_type_cloud_instances.name,
       node_type_cloud_instances.region,
       node_type_cloud_instances.public_ipv4_address,
       node_type_cloud_instances.open_ports,
       clients.name                          as client_name,
       MIN(logs_nodes.created_at)::TIMESTAMP AS node_created_at,
       MAX(logs_nodes.created_at)::TIMESTAMP AS node_updated_at,
       COALESCE(d.status, 'ERROR')          AS CI_deployment_status,
       cloud_instance_state,
       cloud_instance_id
FROM node_type_cloud_instances
         JOIN nodes n on n.id = node_type_cloud_instances.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
         JOIN clients on engagements.client_id = clients.id
         LEFT JOIN logs_nodes on n.id = logs_nodes.node_id
         LEFT JOIN (
             SELECT DISTINCT ON (deployments.node_id) 
                    deployments.node_id, 
                    deployments.status
             FROM deployments
             ORDER BY deployments.node_id, deployments.created_at DESC
         ) d ON d.node_id = n.id
WHERE engagements.id = ANY (sqlc.arg(ids)::uuid[])
  AND users.id = $1
  AND n.is_deleted = false
GROUP BY n.id, ng.id, node_type_cloud_instances.operating_system_image_id, engagements.title,
         node_type_cloud_instances.provider, node_type_cloud_instances.name,
         node_type_cloud_instances.region, node_type_cloud_instances.public_ipv4_address,
         node_type_cloud_instances.open_ports,
         clients.name, d.status, cloud_instance_state, cloud_instance_id
ORDER BY node_updated_at DESC;

-- name: GetInventoryCloudInstancesForUser :many
SELECT 
    n.id                                   AS node_id,
    ng.id                                  AS node_group_id,
    ng.name                                AS node_group_name,
    ng.is_active                           AS node_group_is_active,
    ng.created_at                          AS node_group_created_at,
    ng.updated_at                          AS node_group_updated_at,
    engagements.title,
    node_type_cloud_instances.operating_system_image_id,
    node_type_cloud_instances.provider,
    node_type_cloud_instances.name,
    node_type_cloud_instances.region,
    node_type_cloud_instances.public_ipv4_address,
    node_type_cloud_instances.open_ports,
    clients.name                           AS client_name,
    logs_nodes.id                          AS log_id,
    logs_nodes.created_at                  AS node_created_at,
    logs_nodes.message                     AS log_message,   -- example field, include all you need
    COALESCE(logs_nodes.ip_address::text, '') AS event_ip,
    COALESCE(d.status, 'ERROR')            AS ci_deployment_status,
    cloud_instance_state,
    cloud_instance_id
FROM node_type_cloud_instances
JOIN nodes n 
    ON n.id = node_type_cloud_instances.node_id
JOIN node_groups ng 
    ON n.node_group_id = ng.id
JOIN engagements 
    ON ng.engagement_id = engagements.id
JOIN engagements_users 
    ON engagements.id = engagements_users.engagement_id
JOIN users 
    ON engagements_users.user_id = users.id
JOIN clients 
    ON engagements.client_id = clients.id
LEFT JOIN logs_nodes
    ON n.id = logs_nodes.node_id AND logs_nodes.ip_address IS NOT NULL
LEFT JOIN (
    SELECT DISTINCT ON (deployments.node_id) 
        deployments.node_id, 
        deployments.status
    FROM deployments
    ORDER BY deployments.node_id, deployments.created_at DESC
) d 
    ON d.node_id = n.id
WHERE engagements.id = ANY (sqlc.arg(ids)::uuid[])
  AND users.id = $1
  AND n.is_deleted = false
ORDER BY logs_nodes.created_at DESC NULLS LAST;

-- name: GetEngagementHosts :many
SELECT n.id                                  as node_id,
       ng.id                                 as node_group_id,
       ng.name                               as node_group_name,
       ng.is_active                          as node_group_is_active,
       ng.created_at                         as node_group_created_at,
       ng.updated_at                         as node_group_updated_at,
       engagements.title,
       node_type_hosts.ip_addresses,
       node_type_hosts.alternative_names,
       node_type_hosts.name,
       clients.name                          as client_name,
       MIN(logs_nodes.created_at)::TIMESTAMP AS node_created_at,
       MAX(logs_nodes.created_at)::TIMESTAMP AS node_updated_at
FROM node_type_hosts
         JOIN nodes n on n.id = node_type_hosts.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
         JOIN clients on engagements.client_id = clients.id
         LEFT JOIN logs_nodes on n.id = logs_nodes.node_id

WHERE engagements.id = ANY (sqlc.arg(ids)::uuid[])
  AND users.id = $1
  AND n.is_deleted = false
GROUP BY n.id, ng.id, engagements.title, node_type_hosts.ip_addresses, node_type_hosts.alternative_names,
         node_type_hosts.name, node_type_hosts.node_id, clients.name
ORDER BY node_updated_at DESC;

-- name: GetEngagementEmailAddresses :many
SELECT n.id                                  as node_id,
       ng.id                                 as node_group_id,
       ng.name                               as node_group_name,
       ng.is_active                          as node_group_is_active,
       ng.created_at                         as node_group_created_at,
       ng.updated_at                         as node_group_updated_at,
       engagements.title,
       node_type_email_addresses.email_address,
       clients.name                          as client_name,
       MIN(logs_nodes.created_at)::TIMESTAMP AS node_created_at,
       MAX(logs_nodes.created_at)::TIMESTAMP AS node_updated_at
FROM node_type_email_addresses
         JOIN nodes n on n.id = node_type_email_addresses.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
         JOIN clients on engagements.client_id = clients.id
         LEFT JOIN logs_nodes on n.id = logs_nodes.node_id
WHERE engagements.id = ANY (sqlc.arg(ids)::uuid[])
  AND users.id = $1
  AND n.is_deleted = false
GROUP BY n.id, ng.id, engagements.title, node_type_email_addresses.email_address, node_type_email_addresses.node_id,
         clients.name
ORDER BY node_updated_at DESC;

-- name: GetEngagementPersons :many
SELECT n.id                                  as node_id,
       ng.id                                 as node_group_id,
       ng.name                               as node_group_name,
       ng.is_active                          as node_group_is_active,
       ng.created_at                         as node_group_created_at,
       ng.updated_at                         as node_group_updated_at,
       engagements.title,
       node_type_persons.first_name,
       node_type_persons.last_name,
       node_type_persons.email,
       node_type_persons.company,
       node_type_persons.title,
       clients.name                          as client_name,
       MIN(logs_nodes.created_at)::TIMESTAMP AS node_created_at,
       MAX(logs_nodes.created_at)::TIMESTAMP AS node_updated_at
FROM node_type_persons
         JOIN nodes n on n.id = node_type_persons.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
         JOIN clients on engagements.client_id = clients.id
         LEFT JOIN logs_nodes on n.id = logs_nodes.node_id
WHERE engagements.id = ANY (sqlc.arg(ids)::uuid[])
  AND users.id = $1
  AND n.is_deleted = false
GROUP BY n.id, ng.id, engagements.title, node_type_persons.first_name, node_type_persons.last_name,
         node_type_persons.email, node_type_persons.company, node_type_persons.title, node_type_persons.node_id,
         clients.name
ORDER BY node_updated_at DESC;

-- name: GetEngagementUrls :many
SELECT n.id                                  as node_id,
       ng.id                                 as node_group_id,
       ng.name                               as node_group_name,
       ng.is_active                          as node_group_is_active,
       ng.created_at                         as node_group_created_at,
       ng.updated_at                         as node_group_updated_at,
       engagements.title,
       node_type_urls.url,
       clients.name                          as client_name,
       MIN(logs_nodes.created_at)::TIMESTAMP AS node_created_at,
       MAX(logs_nodes.created_at)::TIMESTAMP AS node_updated_at
FROM node_type_urls
         JOIN nodes n on n.id = node_type_urls.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
         JOIN clients on engagements.client_id = clients.id
         LEFT JOIN logs_nodes on n.id = logs_nodes.node_id
WHERE engagements.id = ANY (sqlc.arg(ids)::uuid[])
  AND users.id = $1
  AND n.is_deleted = false
GROUP BY n.id, ng.id, ng.name, ng.is_active, ng.created_at, ng.updated_at, engagements.title, node_type_urls.url,
         node_type_urls.node_id,
         clients.name
ORDER BY node_updated_at DESC;

-- name: GetEngagementAllNodes :many
SELECT 
    nodes.id            as node_id,
    nodes.node_type,
    nodes.name          as node_name,
    nodes.node_group_id as node_group_id,
    node_groups.name    as node_group_name,
    node_groups.created_at,
    node_groups.updated_at,
    node_type_cloud_instances.cloud_instance_state
FROM nodes
JOIN node_groups ON node_groups.id = nodes.node_group_id
JOIN engagements ON node_groups.engagement_id = engagements.id
JOIN engagements_users ON engagements.id = engagements_users.engagement_id
JOIN users ON engagements_users.user_id = users.id
LEFT JOIN deployments 
    ON deployments.node_id = nodes.id 
    AND deployments.status IN ('SUCCESS', 'WARNING')
LEFT JOIN node_type_cloud_instances 
    ON node_type_cloud_instances.node_id = nodes.id
WHERE engagements.id = $1
  AND users.id = $2
  AND nodes.is_deleted = false;

-- name: GetEngagementNodesGroups :many
SELECT nodes.id                              as node_id,
       nodes.node_type,
       nodes.name                            as node_name,
       engagements.title                     as engagement_name,
       engagements.is_active,
       nodes.node_group_id                   as node_group_id,
       MAX(logs_nodes.created_at)::TIMESTAMP AS node_updated_at,
       clients.name                          as client_name
FROM nodes
         JOIN node_groups on node_groups.id = nodes.node_group_id
         JOIN engagements on node_groups.engagement_id = engagements.id
         JOIN clients on clients.id = engagements.client_id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
         LEFT JOIN logs_nodes ON logs_nodes.node_id = nodes.id
WHERE users.id = $1
  AND nodes.is_deleted = false
GROUP BY nodes.id, nodes.node_type, nodes.name, engagements.title, engagements.is_active,
         nodes.node_group_id, clients.name
ORDER BY node_updated_at DESC;

-- name: GetEngagementGraphRelationships :many
SELECT node_relationships.source_node_id,
       node_relationships.target_node_id,
       node_groups.id as node_group_id
FROM node_relationships
         JOIN nodes on node_relationships.source_node_id = nodes.id
         JOIN node_groups on node_groups.id = nodes.node_group_id
         JOIN engagements on node_groups.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE engagements.id = $1
  AND users.id = $2
  AND is_deleted = false;

-- name: GetEngagementNodeGroupNodes :many
SELECT nodes.id            as node_id,
       nodes.node_type,
       nodes.name          as node_name,
       nodes.node_group_id as node_group_id,
       node_groups.updated_at,
       node_groups.created_at
FROM nodes
         JOIN node_groups on node_groups.id = nodes.node_group_id
         JOIN engagements on node_groups.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE engagements.id = $1
  AND users.id = $2
  AND nodes.is_deleted = false
  AND nodes.node_group_id = $3;

-- name: GetEngagementNodeGroupGraphRelationships :many
SELECT node_relationships.source_node_id,
       node_relationships.target_node_id,
       node_groups.id as node_group_id
FROM node_relationships
         JOIN public.nodes on node_relationships.source_node_id = nodes.id
         JOIN public.node_groups on node_groups.id = nodes.node_group_id
         JOIN public.engagements on node_groups.engagement_id = engagements.id
WHERE engagements.id = $1
  AND nodes.node_group_id = $2
  AND nodes.is_deleted = false;

-- name: GetEngagementTree :many
SELECT node_groups.id                        as node_group_id,
       node_groups.name                      as node_group_name,
       node_groups.is_active                 as node_group_is_active,
       node_groups.created_at                as node_group_created_at,
       node_groups.updated_at                as node_group_updated_at,
       nodes.node_type,
       nodes.name                            as node_name,
       nodes.id                              as node_id,
       MIN(logs_nodes.created_at)::TIMESTAMP AS node_created_at,
       MAX(logs_nodes.created_at)::TIMESTAMP AS node_updated_at
FROM nodes
         JOIN node_groups on nodes.node_group_id = node_groups.id
         JOIN engagements on node_groups.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
         LEFT JOIN logs_nodes on nodes.id = logs_nodes.node_id
WHERE engagements.id = $1
  AND users.id = $2
  AND is_deleted = false
GROUP BY node_groups.id, nodes.id, engagements.id, users.id;

-- name: GetEngagementUsers :many
SELECT *
FROM users
         JOIN engagements_users eu on users.id = eu.user_id
WHERE eu.engagement_id = $1;

-- name: CreateEngagement :one
INSERT INTO engagements (title, wbs_code, is_active, client_id)
VALUES ($1, $2, $3, $4)
RETURNING *;

-- name: DeleteEngagement :exec
UPDATE engagements
SET is_active = false,
    updated_at = NOW()
WHERE id = $1;

-- name: RestoreEngagement :exec
UPDATE engagements 
SET is_active = true, 
    updated_at = NOW()
WHERE id = $1;

-- name: AddAWSAccount :one
INSERT INTO aws_accounts (engagement_id, cloud_account_id, policy_id, status_id, ssh_key_public, error_message, account_creation_status, account_cloud_status, nickname, created_by)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
RETURNING *;

-- name: SetEngagementStatusToError :exec
UPDATE engagements
SET status        = 'ERROR',
    error_message = $1
WHERE id = $2;

-- name: SetEngagementStatusToSuccess :exec
UPDATE engagements
SET status = 'SUCCESS'
WHERE id = $1;

-- name: SetAccountAWSStatus :exec
UPDATE aws_accounts
SET account_cloud_status = $1
WHERE id = $2;

-- -- name: SetEngagementAWSAccountId :exec
-- UPDATE aws_accounts
-- SET cloud_account_id = $1
-- WHERE id = $2;

-- name: SetAWSAccountId :exec
UPDATE aws_accounts
SET cloud_account_id = $1
WHERE id = $2;

-- name: EditEngagement :exec
UPDATE engagements
SET title=$1,
    wbs_code=$2,
    updated_at=$3
WHERE id = $4;

-- name: DeleteUsersFromEngagement :exec
DELETE
FROM engagements_users
WHERE engagement_id = $1
  AND user_id = ANY ($2::uuid[]);

-- name: DeleteUserFromEngagement :exec
DELETE
FROM engagements_users
WHERE engagement_id = $1;

-- name: AddUsersToEngagement :exec
WITH new_users AS (SELECT unnest($2::uuid[]) AS user_id)
INSERT
INTO engagements_users (user_id, engagement_id)
SELECT new_users.user_id, $1
FROM new_users
WHERE NOT EXISTS (SELECT 1
                  FROM engagements_users
                  WHERE engagements_users.user_id = new_users.user_id
                    AND engagements_users.engagement_id = $1);

-- name: AddAdminUserToEngagement :exec
INSERT INTO engagements_users (user_id, engagement_id)
SELECT $1, $2
WHERE NOT EXISTS (
  SELECT 1 
  FROM engagements_users 
  WHERE user_id = $1 
    AND engagement_id = $2
);

-- name: GetNodes :many
SELECT target_node_id
FROM node_relationships
WHERE source_node_id = $1;

-- name: GetEngagementNodeGroup :many
SELECT *
FROM node_groups
WHERE node_groups.id = $1
  AND engagement_id = $2;

-- name: CreateNodeRelationship :exec
INSERT INTO node_relationships (source_node_id, target_node_id)
VALUES ($1, $2);

-- name: AssignUsersToEngagement :exec
INSERT INTO engagements_users (engagement_id, user_id)
VALUES ($1, $2);

-- name: GetClients :many
SELECT name
FROM clients;

-- name: GetClientsById :one
SELECT name
FROM clients
WHERE id = $1;

-- name: GetClientForName :one
SELECT *
FROM clients
WHERE name ILIKE $1;

-- name: CreateClient :exec
INSERT INTO clients (name)
VALUES ($1);

-- name: GetUserForUsername :one
SELECT *
FROM users
WHERE username = $1;

-- name: GetTerraformTemplateContentByName :one
SELECT content
FROM terraform_templates
WHERE name = $1;

-- name: CreateDeployment :exec
INSERT INTO deployments (id, terraform_module, user_id, status, created_at, node_id, engagement_id)
VALUES ($1, $2, $3, $4, $5, $6, $7);

-- name: GetUsers :many
SELECT *
FROM users;

-- name: GetValidAdminUsers :many
SELECT *
FROM users
WHERE id = $1
  AND app_role = 'Admin'
  AND ssh_key IS NOT NULL
  AND custom_username IS NOT NULL;


-- name: GetUsersWithUsername :one
SELECT COUNT(*)
FROM users
WHERE custom_username = $1;

-- name: GetUserCustomUsername :one
SELECT custom_username
FROM users
WHERE id = $1;

-- name: UpdateUserUsername :exec
UPDATE users
SET custom_username = $1
WHERE id = $2;

-- name: GetInstanceSizeMappingsForProvider :many
SELECT size_alias, priority, instance_type
FROM instance_size_mappings
WHERE instance_size_mappings.provider = $1
  AND priority = $2;

-- name: GetInstanceSizePrioritiesForProvider :many
SELECT DISTINCT(priority)
FROM instance_size_mappings
WHERE instance_size_mappings.provider = $1
ORDER BY priority;

-- name: CreateNodeGroup :one
INSERT INTO node_groups (name, is_active, engagement_id, created_at, updated_at)
VALUES ($1, $2, $3, $4, $5)
RETURNING *;

-- name: CreateNode :one
INSERT INTO nodes (node_type, name, node_group_id)
VALUES ($1, $2, $3)
RETURNING *;

-- name: CreateCloudInstanceNode :exec
INSERT INTO node_type_cloud_instances (provider, region, operating_system_image_id, instance_type, name, node_id, open_ports, cloud_instance_id, cloud_instance_state, aws_account_id, azure_tenant_id)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11);

-- name: CreateEmailAddressNode :exec
INSERT INTO node_type_email_addresses (email_address, node_id)
VALUES ($1, $2);

-- name: CreateHostNode :exec
INSERT INTO node_type_hosts (name, ip_addresses, alternative_names, node_id)
VALUES ($1, $2, $3, $4);

-- name: CreatePersonNode :exec
INSERT INTO node_type_persons (first_name, last_name, email, company, title, node_id)
VALUES ($1, $2, $3, $4, $5, $6);

-- name: CreateUrlNode :exec
INSERT INTO node_type_urls (url, node_id)
VALUES ($1, $2);

-- name: UpdateNodeTypeUrlByNodeID :exec
UPDATE node_type_urls
SET url = $2
WHERE node_id = $1;

-- name: GetNodeIDByURL :one
SELECT n.id as node_id
FROM nodes n
JOIN node_type_urls ntu ON n.id = ntu.node_id
WHERE LOWER(ntu.url) = LOWER($1)
LIMIT 1;

-- name: GetDeployments :many
SELECT deployments.*,
       engagements.title,
       users.username,
       nodes.name AS node_name
FROM deployments
         JOIN engagements ON deployments.engagement_id = engagements.id
         JOIN users ON deployments.user_id = users.id
         JOIN nodes ON deployments.node_id = nodes.id
WHERE deployments.engagement_id = ANY ($1::uuid[])
ORDER BY deployments.created_at DESC;


-- name: GetDeployment :one
SELECT deployments.*, engagements.title, users.username, nodes.name AS node_name
FROM deployments
         JOIN engagements ON deployments.engagement_id = engagements.id
         JOIN users ON deployments.user_id = users.id
         JOIN nodes ON deployments.node_id = nodes.id
WHERE deployments.id = $1;

-- name: GetStandardDeployments :many
SELECT deployments.*,
       engagements.title,
       users.username,
       nodes.name AS node_name
FROM deployments
         JOIN engagements ON deployments.engagement_id = engagements.id
         JOIN users ON deployments.user_id = users.id
         JOIN nodes ON deployments.node_id = nodes.id
WHERE users.id = $1;


-- name: GetPendingDeployment :one
SELECT *
FROM deployments as d
WHERE d.status = 'PENDING'
  AND d.engagement_id = $1
ORDER BY created_at
LIMIT 1;

-- name: UpdateDeploymentStatus :exec
UPDATE deployments as d
SET status = $1
WHERE d.id = $2;

-- name: SetDeploymentStatusToError :exec
UPDATE deployments as d
SET status        = 'ERROR',
    error_message = $1
WHERE d.id = $2;

-- name: SetDeploymentStatusToWarning :exec
UPDATE deployments as d
SET status        = 'WARNING',
    error_message = $1
WHERE d.id = $2;


-- name: GetNodeTypes :many
SELECT node_type, COUNT(*)
FROM nodes
         JOIN node_groups on node_groups.id = nodes.node_group_id
         JOIN engagements on node_groups.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE users.id = $1
  AND nodes.is_deleted = false
GROUP BY node_type;

-- name: UpdateNodeTypePerson :one
UPDATE node_type_persons
SET first_name = $1,
    last_name  = $2,
    email      = $3,
    company    = $4,
    title      = $5
WHERE node_id = $6
RETURNING *;

-- name: UpdateNodeTypeUrl :one
UPDATE node_type_urls
SET url = $1
WHERE node_id = $2
RETURNING *;

-- name: UpdateNodeTypeEmailAddress :one
UPDATE node_type_email_addresses
SET email_address = $1
WHERE node_id = $2
RETURNING *;

-- name: UpdateNodeName :exec
UPDATE nodes
SET name = $1
WHERE id = $2;

-- name: UpdateNodeTypeCloudInstance :one
UPDATE node_type_cloud_instances
SET name       = $2,
    open_ports = $3
WHERE node_id = $1
RETURNING *;

-- name: UpdateNodeTypeHost :one
UPDATE node_type_hosts
SET name              = $2,
    ip_addresses      = $3,
    alternative_names = $4
WHERE node_id = $1
RETURNING *;

-- name: InsertActivityLog :exec
INSERT INTO logs_nodes (message, type, user_id, node_id, created_at)
VALUES ($1, $2, $3, $4, $5);

-- name: InsertActivityLogWithIP :exec
INSERT INTO logs_nodes (message, type, user_id, node_id, created_at, ip_address)
VALUES ($1, $2, $3, $4, $5, $6);

-- name: GetNodeActivityLogs :many
SELECT message, type, u.username, created_at
FROM logs_nodes
         JOIN users u on logs_nodes.user_id = u.id
WHERE node_id = $1;

-- name: UpdateNodeGroup :one
UPDATE node_groups ng
SET name = $1
FROM engagements e
         JOIN engagements_users eu ON e.id = eu.engagement_id
         JOIN users u ON eu.user_id = u.id
WHERE ng.engagement_id = e.id
  AND ng.id = $2
  AND u.id = $3
RETURNING *;

-- name: DeleteNodeRelationshipsForNode :exec
DELETE FROM node_relationships
WHERE source_node_id = $1 OR target_node_id = $1;

-- name: DeleteNode :exec
UPDATE nodes
SET is_deleted = true
WHERE id = $1;

-- name: SetDomainNodeToInactiveOrBurned :exec
UPDATE nodes
SET is_deleted = true
WHERE name = $1;

-- name: DeleteNodeRelationship :exec
DELETE
FROM node_relationships
WHERE source_node_id = $1
  AND target_node_id = $2;

-- name: CreateUser :exec
INSERT INTO users (id, full_name, username, app_role)
VALUES ($1, $2, $3, $4);

-- name: GetUserForUserSettings :one
SELECT *
FROM users
WHERE id = $1;

-- name: CreateScript :one
INSERT INTO scripts (name, description, content, script_type, user_id, created_at)
VALUES ($1, $2, $3, 'STANDARD', $4, $5)
RETURNING *;

-- name: EditScript :one
UPDATE scripts
SET name        = $2,
    description = $3,
    content     = $4,
    script_type = 'STANDARD',
    updated_at  = NOW()
WHERE id = $1
  AND user_id = $5
RETURNING *;

-- name: CreateAdminScript :one
INSERT INTO scripts (name, description, content, script_type, user_id, created_at)
VALUES ($1, $2, $3, 'ADMIN', $4, $5)
RETURNING *;

-- name: EditAdminScript :one
UPDATE scripts
SET name        = $2,
    description = $3,
    content     = $4,
    script_type = 'ADMIN',
    updated_at  = NOW()
WHERE id = $1
  AND user_id = $5
RETURNING *;

-- name: GetUserSshKey :one
SELECT ssh_key
FROM users
WHERE id = $1;

-- name: SetUserSshKey :one
UPDATE users
SET ssh_key               = $1,
    ssh_key_label         = $2,
    ssh_key_creation_date = NOW()
WHERE id = $3
RETURNING *;

-- name: RemoveUserSshKey :exec
UPDATE users
SET ssh_key               = NULL,
    ssh_key_label         = NULL,
    ssh_key_creation_date = NULL
WHERE id = $1;

-- name: GetScriptsByUser :many
SELECT *
FROM scripts
WHERE user_id = $1;

-- name: GetAdminScripts :many
SELECT *
FROM scripts
WHERE script_type = 'ADMIN';

-- name: DeleteScript :exec
DELETE
FROM scripts
WHERE id = $1
  AND user_id = $2;

-- name: GetAccountSshPublicKey :one
SELECT ssh_key_public
FROM aws_accounts
WHERE aws_accounts.id = $1;

-- name: GetAccountSshPrivateKey :one
SELECT policy_id, status_id
FROM aws_accounts
WHERE aws_accounts.id = $1;

-- name: GetAzureSshPublicKey :one
SELECT ssh_key_public
FROM azure_tenants
WHERE azure_tenants.id = $1;

-- name: UpdateCloudInstanceIpv4AddressAndID :exec
UPDATE node_type_cloud_instances
SET public_ipv4_address = $2,
    cloud_instance_id = $3
WHERE node_id = $1;

-- name: GetEngagementCloudInstances :many
SELECT *,
       d.status AS CI_deployment_status
FROM node_type_cloud_instances
         JOIN nodes n ON node_type_cloud_instances.node_id = n.id
         JOIN node_groups ng ON n.node_group_id = ng.id
         JOIN engagements e ON ng.engagement_id = e.id
         LEFT JOIN (
              SELECT DISTINCT ON (deployments.node_id) 
                      deployments.node_id, 
                      deployments.status
              FROM deployments
              ORDER BY deployments.node_id, deployments.created_at DESC
         ) d ON d.node_id = node_type_cloud_instances.node_id
WHERE e.id = $1
  AND n.is_deleted = false;

-- name: GetUsersFromIDs :many
SELECT *
FROM users
WHERE id = ANY ($1::uuid[]);

-- name: InsertAssignmentLog :exec
INSERT INTO logs_assignments (message, type, status, user_id, user_custom_username_used, node_id)
VALUES ($1, $2, $3, $4, $5, $6);

-- name: GetAssignmentLogs :many
SELECT logs_assignments.id,
       logs_assignments.message,
       logs_assignments.type,
       logs_assignments.status,
       user_id,
       user_custom_username_used,
       node_id,
       nodes.name,
       logs_assignments.created_at
FROM logs_assignments
         JOIN nodes
              ON nodes.id = logs_assignments.node_id
         JOIN node_groups
              ON node_groups.id = nodes.node_group_id
         JOIN engagements
              ON node_groups.engagement_id = engagements.id
WHERE engagements.id = $1;

-- name: GetAllAssignmentsLogs :many
SELECT logs_assignments.id,
       logs_assignments.message,
       logs_assignments.type,
       logs_assignments.status,
       user_id,
       user_custom_username_used,
       node_id,
       nodes.name,
       logs_assignments.created_at
FROM logs_assignments
         JOIN nodes
              ON nodes.id = logs_assignments.node_id
         JOIN node_groups
              ON node_groups.id = nodes.node_group_id
         JOIN engagements
              ON node_groups.engagement_id = engagements.id;

-- name: GetNodeIPHistory :many
SELECT
  ip_address::text AS ip_address,
  created_at
FROM logs_nodes
WHERE node_id = $1 AND ip_address IS NOT NULL
ORDER BY created_at DESC;

-- name: CheckNodeGroupId :one
SELECT CASE
           WHEN n1.node_group_id = n2.node_group_id THEN 'Same'
           ELSE 'Different'
           END AS node_group_comparison
FROM nodes n1
         JOIN
     nodes n2
     ON
         n1.id = $1 AND n2.id = $2
WHERE n1.is_deleted = false
  AND n2.is_deleted = false;

-- name: UpdateNodeGroupId :exec
UPDATE nodes
SET node_group_id = $1
WHERE id = ANY ($2::uuid[]);

-- name: GetNodeGroupId :one
SELECT node_group_id
FROM nodes
WHERE id = $1;

-- name: GetNodeTypeAndName :one
SELECT node_type, name
FROM nodes
WHERE id = $1;

-- name: GetSourceRelationships :many
SELECT source_node_id
FROM node_relationships
WHERE target_node_id = $1;

-- name: DeleteRelationship :exec
DELETE
FROM node_relationships
WHERE (source_node_id, target_node_id) IN (SELECT source_node_id::uuid, target_node_id::uuid
                                           FROM (VALUES ($1, $2)) AS t(source_node_id, target_node_id));

-- name: GetUserByAzureID :one
SELECT *
FROM users
WHERE id = $1;

-- name: UpdateUser :exec
UPDATE users
SET full_name = $2,
    app_role = $3
WHERE id = $1;

-- name: MarkUserInactive :exec
UPDATE users
SET is_inActive = false
WHERE username = $1;

-- name: GetAllUsers :many
SELECT *
FROM users;

-- name: GetEligibleAdminUsers :many
SELECT username
FROM users
WHERE app_role = 'Admin'
  AND custom_username IS NOT NULL
  AND ssh_key IS NOT NULL;

-- name: GetCloudInstancesWithRegionAndSecretNameForAccount :many
SELECT 
    aws.cloud_account_id,
    ntci.cloud_instance_id,
	  ntci.region,
	  aws.id as secret_id
FROM node_type_cloud_instances ntci
JOIN aws_accounts aws ON ntci.aws_account_id = aws.id
WHERE aws.account_cloud_status = 'ACTIVE'
AND ntci.cloud_instance_state NOT IN ('terminated', 'error')
AND aws.cloud_account_id IS NOT NULL
AND ntci.cloud_instance_id IS NOT NULL;


-- name: MarkInstancesAsTerminatedForInactiveAccounts :exec
UPDATE node_type_cloud_instances ntci
SET cloud_instance_state = 'terminated'
FROM aws_accounts aws
WHERE ntci.aws_account_id = aws.id
  AND aws.account_cloud_status != 'ACTIVE'
  AND ntci.cloud_instance_id IS NOT NULL
  AND ntci.cloud_instance_state NOT IN ('terminated', 'error');


-- name: SetMissingCloudInstancesAsTerminated :exec
UPDATE node_type_cloud_instances ntci
SET cloud_instance_state = 'terminated'
WHERE cloud_instance_id = ANY($1::text[]);

-- name: UpdateCloudInstanceAWSState :exec
UPDATE node_type_cloud_instances ntci
SET cloud_instance_state        = $1
WHERE ntci.cloud_instance_id = $2;


-- name: UpdateCloudInstanceAWSStateByNodeAndID :one
UPDATE node_type_cloud_instances
SET cloud_instance_state = $1
WHERE node_id = $2 AND cloud_instance_id = $3
RETURNING 1;



-- name: UpdateCloudInstanceAWSStateByAwsAccountAndID :one
UPDATE node_type_cloud_instances
SET cloud_instance_state = $1
WHERE aws_account_id = $2 AND cloud_instance_id = $3
RETURNING 1;


-- name: UpdateCloudInstanceIPByNodeAndID :one
UPDATE node_type_cloud_instances
SET public_ipv4_address = $1
WHERE node_id = $2 AND cloud_instance_id = $3
RETURNING 1;

-- name: MarkStuckInstancesAsFailed :exec
UPDATE node_type_cloud_instances ntci
SET cloud_instance_state = 'error'
WHERE ntci.cloud_instance_id IS NULL
AND ntci.cloud_instance_state = 'new'
AND (
    -- Case 1: Deployment exists and is in ERROR/PENDING for too long
    EXISTS (
        SELECT 1 
        FROM deployments d
        WHERE d.node_id = ntci.node_id
        AND (
            d.status = 'ERROR'
            OR (d.status = 'PENDING' AND d.created_at < NOW() - INTERVAL '10 minutes')
        )
    )
    -- Case 2: No deployment exists at all for this node_id
    OR NOT EXISTS (
        SELECT 1 
        FROM deployments d
        WHERE d.node_id = ntci.node_id
    )
);


-- name: GetAWSAccountsAccountID :many
SELECT e.id,
       e.account_creation_status,
       e.cloud_account_id,
       e.account_cloud_status
FROM aws_accounts e
WHERE e.cloud_account_id IS NOT NULL
AND e.account_creation_status = 'SUCCESS';


-- name: UpdateInstanceTypes :exec
INSERT INTO instance_size_mappings (
    provider,
    size_alias,
    priority,
    instance_type
) VALUES (
    $1, $2, $3, $4
)
ON CONFLICT (provider, size_alias, priority) 
DO UPDATE SET
    instance_type = EXCLUDED.instance_type
RETURNING *;

-- name: GetAllInstanceSizeMappings :many
SELECT id, provider, size_alias, priority, instance_type
FROM instance_size_mappings
ORDER BY provider, size_alias, priority;

-- name: DeleteInstanceType :exec
DELETE FROM instance_size_mappings 
WHERE provider = $1 
AND size_alias = $2 
AND priority = $3;

-- name: GetInstanceSizeMappingsForRegionCheck :many
SELECT DISTINCT instance_type, size_alias, priority
FROM instance_size_mappings
WHERE provider = $1
ORDER BY priority;

-- name: SyncPrioritizedRegions :exec
WITH new_values (name) AS (
    -- Values to be inserted/updated
    SELECT unnest($1::text[])
),
-- Delete records that exist in the table but not in the new values
-- If $1 is empty, this will delete all records since the subquery returns no rows
cleanup AS (
    DELETE FROM prioritized_regions
    WHERE NOT EXISTS (
        SELECT 1 FROM unnest($1::text[]) AS new_name
        WHERE prioritized_regions.name = new_name
    )
),
-- Insert new records that don't already exist
insertions AS (
    INSERT INTO prioritized_regions (name)
    SELECT name FROM new_values
    WHERE NOT EXISTS (
        SELECT 1 FROM prioritized_regions
        WHERE prioritized_regions.name = new_values.name
    )
)
SELECT 1;

-- name: GetPrioritizedRegions :many
SELECT *
FROM prioritized_regions;

-- name: GetInstanceSizeMappingsForCategory :many
SELECT id, provider, size_alias, priority, instance_type
FROM instance_size_mappings
WHERE provider = $1
AND size_alias = $2
ORDER BY provider, size_alias, priority;

-- name: SetAWSAccountStatusToError :exec
UPDATE aws_accounts
SET account_creation_status        = 'ERROR',
    error_message = $1
WHERE id = $2;

-- name: SetAWSAccountStatusToSuccess :exec
UPDATE aws_accounts
SET account_creation_status        = 'SUCCESS'
WHERE id = $1;

-- name: SetAzureTenantStatusToError :exec
UPDATE azure_tenants
SET creation_status        = 'ERROR',
    error_message = $1
WHERE id = $2;

-- name: SetAzureTenantStatusToSuccess :exec
UPDATE azure_tenants
SET creation_status        = 'SUCCESS'
WHERE id = $1;

-- name: SetAzureSubscriptionStatus :exec
UPDATE azure_tenants
SET account_cloud_status        = $2
WHERE id = $1;

-- name: UpdateCloudInstanceAzureState :exec
UPDATE node_type_cloud_instances ntci
SET cloud_instance_state        = $1
WHERE ntci.cloud_instance_id = $2;

-- name: GetAzureCloudInstancesWithRegionAndTenantInfo :many
SELECT ntci.cloud_instance_id, ntci.azure_tenant_id, at.subscription_id
FROM node_type_cloud_instances ntci
JOIN azure_tenants at ON ntci.azure_tenant_id = at.id
WHERE LOWER(ntci.provider::text) = 'azure'
  AND ntci.cloud_instance_id IS NOT NULL
  AND ntci.cloud_instance_state NOT IN ('terminated', 'error')
  AND at.account_cloud_status = 'Enabled';

-- name: MarkInstancesAsTerminatedForInactiveAzureTenants :exec
UPDATE node_type_cloud_instances
SET cloud_instance_state = 'terminated'
WHERE azure_tenant_id IN (
    SELECT id FROM azure_tenants
    WHERE account_cloud_status != 'Enabled' OR account_cloud_status IS NULL
)
AND cloud_instance_state NOT IN ('terminated', 'error');

-- name: SaveAzureTenantData :one
INSERT INTO azure_tenants (
    engagement_id,
    tenant_id,
    subscription_id,
    policy_id, status_id, ssh_key_public, creation_status, secrets_saved, account_cloud_status
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9
)
RETURNING id;

-- name: UpdateAzureTenantSecretsStatus :exec
UPDATE azure_tenants
SET secrets_saved        = 'TRUE'
WHERE id = $1;

-- name: GetEngagementStatus :one
SELECT status FROM engagements as e
WHERE e.id = $1;

-- name: ListAWSAccountsByEngagementID :many
SELECT a.id, a.nickname, a.cloud_account_id, a.account_cloud_status, a.account_creation_status, a.created_at, u.username AS created_by  
FROM aws_accounts a
JOIN users u ON a.created_by = u.id 
WHERE engagement_id = $1
ORDER BY created_at DESC;

-- name: ListAWSAccountsByEngagementIDAndStatus :many
SELECT a.id, a.nickname, a.cloud_account_id  
FROM aws_accounts a
JOIN users u ON a.created_by = u.id 
WHERE engagement_id = $1
  AND account_creation_status = $2
  AND account_cloud_status = $3
ORDER BY created_at DESC;


-- name: ListAzureTenantsByEngagementID :many
SELECT id, tenant_id, subscription_id, creation_status, created_at, secrets_saved, account_cloud_status
FROM azure_tenants
WHERE engagement_id = $1
ORDER BY created_at DESC;

-- name: ListAzureTenantsByEngagementIDAndStatus :many
SELECT id, tenant_id
FROM azure_tenants
WHERE engagement_id = $1
    AND account_cloud_status = $2
    AND creation_status = $4
    AND subscription_id IS NOT NULL
    AND secrets_saved = $3
ORDER BY created_at DESC;

-- name: GetAzureTenantBySubscriptionID :one
SELECT * 
FROM azure_tenants
WHERE subscription_id = $1;

-- name: GetAzureTenantByTenantID :one
SELECT * 
FROM azure_tenants
WHERE tenant_id = $1;

-- name: GetAzureSubscriptionIDById :one
SELECT subscription_id 
FROM azure_tenants
WHERE id = $1;

-- name: ListAzureTenantsWithSubscriptions :many
SELECT id, subscription_id, account_cloud_status
FROM azure_tenants
WHERE subscription_id IS NOT NULL;

-- name: GetAzureTenantSshPrivateKey :one
SELECT policy_id, status_id
FROM azure_tenants
WHERE azure_tenants.id = $1;


-- name: CreateDomain :one
INSERT INTO domains (
    url, 
    registrar, 
    purchase_date, 
    renewal_date, 
    status, 
    engagement, 
    client, 
    age
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8
)
RETURNING *;

-- name: GetDomains :many
SELECT * FROM domains
ORDER BY created_at DESC;

-- name: GetDomainsWithHistory :many
SELECT
    ln.message,
    ln.type,
	  ln.created_at,
    u.username AS user_username,
    d.url AS domain_url,
    d.registrar,
    d.engagement,
    d.client,
    d.age
FROM logs_nodes ln
LEFT JOIN users u ON ln.user_id = u.id
LEFT JOIN nodes n ON ln.node_id = n.id
LEFT JOIN node_type_urls ntu ON n.id = ntu.node_id
LEFT JOIN domains d ON LOWER(d.url) = LOWER(ntu.url)
ORDER BY ln.created_at DESC;

-- name: GetDomainsByUrl :many
SELECT * FROM domains
WHERE url = $1;

-- name: UpdateDomain :one
UPDATE domains
SET
    url = $2,
    registrar = $3,
    purchase_date = $4,
    renewal_date = $5,
    status = $6,
    engagement = $7,
    client = $8,
    age = $9
WHERE id = $1
RETURNING *;

-- name: UpdateDomainField :one
UPDATE domains
SET
    url = CASE WHEN $2 = 'url' THEN $3 ELSE url END,
    registrar = CASE WHEN $2 = 'registrar' THEN $3 ELSE registrar END,
    purchase_date = CASE WHEN $2 = 'purchase_date' THEN $3::date ELSE purchase_date END,
    renewal_date = CASE WHEN $2 = 'renewal_date' THEN $3::date ELSE renewal_date END,
    status = CASE WHEN $2 = 'status' THEN $3::domain_status_enum ELSE status END,
    engagement = CASE WHEN $2 = 'engagement' THEN $3 ELSE engagement END,
    client = CASE WHEN $2 = 'client' THEN $3 ELSE client END,
    age = CASE WHEN $2 = 'age' THEN $3::integer ELSE age END
WHERE id = $1
RETURNING *;

-- name: DeleteDomain :exec
DELETE FROM domains
WHERE id = $1;

-- name: SetDomainUnAssigned :exec
UPDATE domains
set status = 'UNASSIGNED'
WHERE url = $1;

-- name: SetDomainQuarantine :exec
UPDATE domains
set status = 'QUARANTINE'
WHERE url = $1;

-- name: SetDomainBurned :exec
UPDATE domains
set status = 'BURNED'
WHERE id = $1;

-- name: EditDomainForAssignment :exec
UPDATE domains
SET client = $1
WHERE id = $2;

-- name: UpdateDomainEngagementClient :exec
UPDATE domains
SET
    engagement = $2,
    client = $3,
    status = $4
WHERE id = $1
RETURNING *;

-- name: ClearDomainEngagementClientValues :exec
UPDATE domains
SET
    engagement = '',
    client = '',
    status = $2
WHERE id = $1
RETURNING *;

-- name: GetUniqueRegistrars :many
SELECT DISTINCT registrar
FROM domains
WHERE registrar IS NOT NULL AND registrar != ''
ORDER BY registrar;

-- name: GetDomainByID :one
SELECT * FROM domains
WHERE id = $1;

-- name: GetNodeIDByDomainURL :one
SELECT n.id as node_id
FROM nodes n
JOIN node_type_urls ntu ON n.id = ntu.node_id
JOIN domains d ON LOWER(d.url) = LOWER(ntu.url)
WHERE d.id = $1
LIMIT 1;

-- name: GetNodesByNameAndType :many
SELECT *
FROM nodes
WHERE name = $1 AND node_type = $2;

-- name: ReactivateNode :exec
UPDATE nodes
SET is_deleted = false
WHERE id = $1;

-- name: UpdateNodeNodeGroup :exec
UPDATE nodes
SET node_group_id = $2
WHERE id = $1;

-- name: SetDomainAssigned :exec
UPDATE domains
SET status = 'ASSIGNED'
WHERE url = $1;