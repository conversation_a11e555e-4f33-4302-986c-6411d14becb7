-- Delete statements
-- drop table terraform_templates;
-- drop table engagements_users;
-- drop table node_type_cloud_instances;
-- drop table node_type_email_addresses;
-- drop table node_type_hosts;
-- drop table node_type_persons;
-- drop table node_type_urls;
-- drop table node_relationships;
-- drop table instance_size_mappings;
-- drop table logs_nodes;
-- drop table logs_assignments;
-- drop table scripts;
-- drop table deployments;
-- drop table users;
-- drop table nodes;
-- drop table node_groups;
-- drop table engagements;
-- drop table clients;
-- drop type deployment_status_enum;
-- drop type engagement_status_enum;
-- drop type logs_assignments_type_enum;
-- drop type logs_assignments_status_enum;
-- drop type logs_nodes_type_enum;
-- drop type node_type_enum;
-- drop type provider_enum;
-- drop type script_type_enum;

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE terraform_templates
(
    id      UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name    TEXT NOT NULL,
    content TEXT NOT NULL
);

CREATE TABLE clients
(
    id   UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL
);

CREATE TYPE engagement_status_enum AS ENUM ('PENDING', 'SUCCESS', 'ERROR');

CREATE TABLE engagements
(
    id                       UUID PRIMARY KEY       DEFAULT uuid_generate_v4(),
    title                    VARCHAR(250)           NOT NULL,
    wbs_code                 TEXT                   NOT NULL,
    is_active                BOOLEAN                NOT NULL,
    client_id                UUID                   NOT NULL,
    created_at               TIMESTAMP              NOT NULL DEFAULT current_timestamp,
    updated_at               TIMESTAMP              NOT NULL DEFAULT current_timestamp,
    status                   engagement_status_enum NOT NULL DEFAULT 'PENDING',
    error_message            TEXT,
    FOREIGN KEY (client_id) REFERENCES clients (id)
);

CREATE TABLE aws_accounts (
    id                UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    engagement_id     UUID NOT NULL,
    cloud_account_id        VARCHAR,  -- can be NULL at first, filled after AWS account creation
    policy_id         VARCHAR NOT NULL,
    status_id         VARCHAR NOT NULL,
    ssh_key_public    VARCHAR NOT NULL,
    created_at        TIMESTAMP NOT NULL DEFAULT current_timestamp,
    error_message     TEXT,
    account_creation_status    TEXT,
    account_cloud_status TEXT,
    nickname          VARCHAR NOT NULL,
    created_by        UUID NOT NULL,
    FOREIGN KEY (engagement_id) REFERENCES engagements(id) ON DELETE CASCADE,
    CONSTRAINT unique_engagement_nickname UNIQUE (engagement_id, nickname)
);


CREATE TABLE users
(
    id                    UUID PRIMARY KEY,
    username              TEXT NOT NULL,
    custom_username       TEXT,
    full_name             TEXT ,
    app_role              TEXT,
    ssh_key               TEXT,
    ssh_key_label         TEXT,
    ssh_key_creation_date TIMESTAMP,
    is_inActive           BOOLEAN
);

-- Many-to-many table that associates Engagements with Users.
-- A User can belong to many Engagements
CREATE TABLE engagements_users
(
    engagement_id UUID NOT NULL,
    user_id       UUID NOT NULL,
    PRIMARY KEY (engagement_id, user_id),
    FOREIGN KEY (engagement_id) REFERENCES engagements (id),
    FOREIGN KEY (user_id) REFERENCES users (id)
);

CREATE TYPE node_type_enum AS ENUM ('CLOUD_INSTANCE', 'EMAIL_ADDRESS', 'HOST', 'PERSON', 'URL');

-- Node Groups are associated with a single Engagement
CREATE TABLE node_groups
(
    id            UUID PRIMARY KEY   DEFAULT uuid_generate_v4(),
    name          TEXT      NOT NULL,
    is_active     BOOLEAN   NOT NULL,
    created_at    TIMESTAMP NOT NULL DEFAULT current_timestamp,
    updated_at    TIMESTAMP NOT NULL DEFAULT current_timestamp,
    engagement_id UUID      NOT NULL,
    FOREIGN KEY (engagement_id) REFERENCES engagements (id)
);

-- Nodes are associated with a single Node Group that in turn belongs to a single Engagement
CREATE TABLE nodes
(
    id            UUID PRIMARY KEY        DEFAULT uuid_generate_v4(),
    node_type     node_type_enum NOT NULL,
    name          TEXT           NOT NULL,
    node_group_id UUID           NOT NULL,
    is_deleted    BOOLEAN        NOT NULL DEFAULT FALSE,
    FOREIGN KEY (node_group_id) REFERENCES node_groups (id)
);

CREATE TYPE provider_enum AS ENUM ('AWS', 'AZURE', 'GCP');
CREATE TYPE ci_state_enum AS ENUM ('pending', 'running', 'stopping', 'stopped', 'shutting-down', 'terminated', 'error', 'new');

-- Cloud Instances are a type of Node
CREATE TABLE node_type_cloud_instances
(
    provider                  provider_enum NOT NULL,
    region                    TEXT          NOT NULL,
    operating_system_image_id TEXT          NOT NULL,
    instance_type             TEXT          NOT NULL,
    name                      TEXT          NOT NULL,
    open_ports                INTEGER[]     NOT NULL,
    public_ipv4_address       inet,
    node_id                   UUID          NOT NULL,
    cloud_instance_state      ci_state_enum DEFAULT 'new',
    cloud_instance_id         TEXT,
    aws_account_id          UUID,    
    azure_tenant_id         UUID,    
    FOREIGN KEY (node_id) REFERENCES nodes (id) ON DELETE CASCADE,
    FOREIGN KEY (aws_account_id) REFERENCES aws_accounts (id),
    FOREIGN KEY (azure_tenant_id) REFERENCES azure_tenants (id)
);

CREATE TABLE node_type_email_addresses
(
    email_address TEXT NOT NULL,
    node_id       UUID NOT NULL,
    FOREIGN KEY (node_id) REFERENCES nodes (id) ON DELETE CASCADE
);

CREATE TABLE node_type_hosts
(
    name              TEXT   NOT NULL,
    ip_addresses      inet[] NOT NULL,
    alternative_names TEXT[] NOT NULL,
    node_id           UUID   NOT NULL,
    FOREIGN KEY (node_id) REFERENCES nodes (id) ON DELETE CASCADE
);

CREATE TABLE node_type_persons
(
    first_name TEXT NOT NULL,
    last_name  TEXT NULL,
    email      TEXT NULL,
    company    TEXT NULL,
    title      TEXT NULL,
    node_id    UUID NULL,
    FOREIGN KEY (node_id) REFERENCES nodes (id) ON DELETE CASCADE
);


CREATE TYPE deployment_status_enum AS ENUM ('PENDING', 'IN-PROGRESS', 'SUCCESS', 'WARNING', 'ERROR');

-- Many Deployments can be associated with a single Node, a User that initiates a Deployment is also associated with a Deployment
CREATE TABLE deployments
(
    id               UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    terraform_module TEXT                   NOT NULL,
    status           deployment_status_enum NOT NULL,
    created_at       TIMESTAMP              NOT NULL,
    error_message    TEXT,
    user_id          UUID                   NOT NULL,
    node_id          UUID                   NOT NULL,
    engagement_id    UUID                   NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (node_id) REFERENCES nodes (id),
    FOREIGN KEY (engagement_id) REFERENCES engagements (id)
);

CREATE TABLE node_relationships
(
    id             BIGSERIAL PRIMARY KEY,
    source_node_id UUID NOT NULL,
    target_node_id UUID NOT NULL,
    FOREIGN KEY (source_node_id) REFERENCES nodes (id),
    FOREIGN KEY (target_node_id) REFERENCES nodes (id)
);

CREATE TABLE instance_size_mappings
(
    id            BIGSERIAL PRIMARY KEY,
    provider      provider_enum NOT NULL,
    size_alias    TEXT          NOT NULL,
    priority      INT           NOT NULL,
    instance_type TEXT          NOT NULL,
    CONSTRAINT instance_size_mappings_provider_size_alias_priority_key 
        UNIQUE (provider, size_alias, priority)
);

CREATE TABLE prioritized_regions (
  id   SERIAL PRIMARY KEY,
  name VARCHAR(255)
);


CREATE TYPE logs_nodes_type_enum AS ENUM (
    'NODE_CREATION',
    'NODE_UPDATE',
    'NODE_DELETION',
    'INBOUND_RELATIONSHIP_CREATION',
    'INBOUND_RELATIONSHIP_DELETION',
    'OUTBOUND_RELATIONSHIP_CREATION',
    'OUTBOUND_RELATIONSHIP_DELETION',
    'DOMAIN_ASSIGNED',
    'DOMAIN_BURNED',
    'DOMAIN_UNASSIGNED',
    'DOMAIN_QUARANTINED',
    'DOMAIN_EXPIRED'
);

CREATE TABLE logs_nodes
(
    id         BIGSERIAL PRIMARY KEY,
    message    TEXT                 NOT NULL,
    type       logs_nodes_type_enum NOT NULL,
    user_id    UUID                 NOT NULL,
    node_id    UUID                 NOT NULL,
    created_at TIMESTAMP            NOT NULL DEFAULT current_timestamp,
    ip_address inet,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (node_id) REFERENCES nodes (id)
        ON DELETE CASCADE
);

CREATE TYPE logs_assignments_type_enum AS ENUM (
    'SSH_CONNECTION',
    'USER_ASSIGNMENT',
    'USER_REMOVAL');

CREATE TYPE logs_assignments_status_enum AS ENUM (
    'SUCCESS',
    'ERROR');

CREATE TABLE logs_assignments
(
    id                        UUID PRIMARY KEY                      DEFAULT uuid_generate_v4(),
    message                   TEXT                         NOT NULL,
    type                      logs_assignments_type_enum   NOT NULL,
    status                    logs_assignments_status_enum NOT NULL,
    user_id                   UUID,
    user_custom_username_used TEXT,
    node_id                   UUID                         NOT NULL,
    created_at                TIMESTAMP                    NOT NULL DEFAULT current_timestamp,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (node_id) REFERENCES nodes (id)
        ON DELETE CASCADE
);

-- Scripts are either admin or standard
CREATE TYPE script_type_enum AS ENUM ('ADMIN', 'STANDARD');

CREATE TABLE scripts
(
    id          UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name        TEXT             NOT NULL,
    description TEXT             NOT NULL,
    content     TEXT             NOT NULL,
    script_type script_type_enum NOT NULL,
    created_at  TIMESTAMP        DEFAULT CURRENT_TIMESTAMP,
    updated_at  TIMESTAMP        DEFAULT CURRENT_TIMESTAMP,
    user_id     UUID             NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users (id)
);


CREATE TABLE azure_tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    engagement_id UUID NOT NULL,
    tenant_id VARCHAR(255) NOT NULL UNIQUE,
    subscription_id TEXT UNIQUE,
    error_message TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT current_timestamp,
    creation_status TEXT DEFAULT 'PENDING',
    account_cloud_status TEXT,
    policy_id         VARCHAR NOT NULL,
    status_id         VARCHAR NOT NULL,
    ssh_key_public    VARCHAR NOT NULL,
    secrets_saved     BOOLEAN NOT NULL DEFAULT FALSE,
    FOREIGN KEY (engagement_id) REFERENCES engagements(id) ON DELETE CASCADE
);

CREATE TABLE node_type_urls
(
    url     TEXT NOT NULL,
    node_id UUID NOT NULL,
    FOREIGN KEY (node_id) REFERENCES nodes (id) ON DELETE CASCADE
);

CREATE TYPE domain_status_enum AS ENUM ('UNASSIGNED', 'ASSIGNED', 'QUARANTINE', 'BURNED', 'EXPIRED');

-- Create a separate domains table for direct domain imports
CREATE TABLE domains (
    id              UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    url             TEXT NOT NULL,
    registrar       TEXT,
    purchase_date   DATE,
    renewal_date    DATE,
    status          domain_status_enum  DEFAULT 'UNASSIGNED',
    engagement      TEXT,
    client          TEXT,
    age             INTEGER,
    created_at      TIMESTAMP NOT NULL DEFAULT current_timestamp
);
